"""
演示 post_process.py 的改进功能

展示新的绘图功能：
1. 传递函数计算与绘图分离的优势
2. 方形色块绘图效果
3. 相位周期性处理的效果
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import List
import time

# 导入需要演示的模块
from sweeper400.analyze.post_process import (
    calculate_transfer_function,
    plot_transfer_function_discrete_distribution,
    plot_transfer_function_interpolated_distribution,
    TransferFunctionResult,
)
from sweeper400.use.sweeper import Point2D


def create_demo_data() -> List[TransferFunctionResult]:
    """
    创建演示用的传递函数数据
    """
    print("创建演示数据...")
    
    # 创建一个5x5的网格
    results = []
    
    for x in range(5):
        for y in range(5):
            # 创建有趣的幅值比分布（模拟声场干涉模式）
            amp_ratio = 1.0 + 0.5 * np.sin(0.8 * x) * np.cos(0.6 * y)
            
            # 创建相位分布，包含一些跳跃来演示周期性处理
            phase = 0.4 * x + 0.3 * y
            
            # 在某些区域添加相位跳跃
            if x >= 3 and y >= 2:
                phase += np.pi  # 添加π的跳跃
            
            # 归一化到[-π, π]区间
            phase = np.arctan2(np.sin(phase), np.cos(phase))
            
            result: TransferFunctionResult = {
                "position": Point2D(x=float(x), y=float(y)),
                "amp_ratio": amp_ratio,
                "phase_shift": phase,
            }
            results.append(result)
    
    print(f"创建了 {len(results)} 个数据点")
    return results


def demo_calculation_separation():
    """
    演示传递函数计算与绘图分离的优势
    """
    print("\n=== 演示1: 传递函数计算与绘图分离 ===")
    
    # 创建演示数据
    tf_results = create_demo_data()
    
    # 模拟多次使用同一计算结果
    print("一次计算，多次绘图...")
    
    start_time = time.time()
    
    # 第一次绘图：离散分布
    print("绘制离散分布图...")
    fig1, _ = plot_transfer_function_discrete_distribution(tf_results)
    plt.close(fig1)
    
    # 第二次绘图：插值分布
    print("绘制插值分布图...")
    fig2, _ = plot_transfer_function_interpolated_distribution(tf_results)
    plt.close(fig2)
    
    # 第三次绘图：不同参数的插值分布
    print("绘制不同参数的插值分布图...")
    fig3, _ = plot_transfer_function_interpolated_distribution(
        tf_results, 
        interpolation_method="nearest",
        grid_resolution=50
    )
    plt.close(fig3)
    
    end_time = time.time()
    print(f"三次绘图总耗时: {end_time - start_time:.3f} 秒")
    print("✓ 优势：计算一次，可重复使用结果进行不同的可视化")


def demo_square_blocks():
    """
    演示方形色块绘图效果
    """
    print("\n=== 演示2: 方形色块绘图效果 ===")
    
    tf_results = create_demo_data()
    
    print("绘制方形色块分布图...")
    fig, (ax1, ax2) = plot_transfer_function_discrete_distribution(tf_results)
    
    # 添加说明文字
    fig.suptitle("方形色块绘图效果演示\n每个方块代表一个测量点", fontsize=16)
    
    # 保存图片
    fig.savefig("demo_square_blocks.png", dpi=150, bbox_inches="tight")
    print("✓ 图片已保存为: demo_square_blocks.png")
    
    plt.close(fig)


def demo_phase_periodicity():
    """
    演示相位周期性处理效果
    """
    print("\n=== 演示3: 相位周期性处理效果 ===")
    
    # 创建包含明显相位跳跃的数据
    results = []
    
    for x in range(6):
        for y in range(6):
            # 创建在某个区域有相位跳跃的分布
            if x < 3:
                phase = -np.pi + 0.2 * y  # 左侧接近-π
            else:
                phase = np.pi - 0.2 * y   # 右侧接近π
            
            result: TransferFunctionResult = {
                "position": Point2D(x=float(x), y=float(y)),
                "amp_ratio": 1.0,
                "phase_shift": phase,
            }
            results.append(result)
    
    print("绘制包含相位跳跃的插值分布图...")
    fig, (ax1, ax2) = plot_transfer_function_interpolated_distribution(
        results,
        interpolation_method="linear",
        grid_resolution=50
    )
    
    # 添加说明文字
    fig.suptitle("相位周期性处理演示\n左右两侧相位从-π跳跃到π，插值算法正确处理了周期性", fontsize=14)
    
    # 保存图片
    fig.savefig("demo_phase_periodicity.png", dpi=150, bbox_inches="tight")
    print("✓ 图片已保存为: demo_phase_periodicity.png")
    
    plt.close(fig)


def demo_comparison():
    """
    演示新旧绘图方法的对比
    """
    print("\n=== 演示4: 新旧绘图方法对比 ===")
    
    tf_results = create_demo_data()
    
    # 创建对比图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 提取数据用于手动绘制"旧版本"效果
    x_coords = np.array([result["position"].x for result in tf_results])
    y_coords = np.array([result["position"].y for result in tf_results])
    amp_ratios = np.array([result["amp_ratio"] for result in tf_results])
    phase_shifts = np.array([result["phase_shift"] for result in tf_results])
    
    # 旧版本风格：圆点
    scatter1 = ax1.scatter(x_coords, y_coords, c=amp_ratios, cmap="viridis", s=100)
    ax1.set_title("旧版本：圆点绘图", fontsize=14)
    ax1.set_xlabel("X 坐标 (mm)")
    ax1.set_ylabel("Y 坐标 (mm)")
    fig.colorbar(scatter1, ax=ax1, label="幅值比")
    
    scatter2 = ax2.scatter(x_coords, y_coords, c=phase_shifts, cmap="twilight", s=100)
    ax2.set_title("旧版本：圆点绘图", fontsize=14)
    ax2.set_xlabel("X 坐标 (mm)")
    ax2.set_ylabel("Y 坐标 (mm)")
    fig.colorbar(scatter2, ax=ax2, label="相位差 (rad)")
    
    # 新版本风格：方形色块（手动绘制到指定的axes）
    from matplotlib.patches import Rectangle
    
    # 计算网格间距
    unique_x = np.unique(x_coords)
    unique_y = np.unique(y_coords)
    dx = np.min(np.diff(unique_x)) if len(unique_x) > 1 else 1.0
    dy = np.min(np.diff(unique_y)) if len(unique_y) > 1 else 1.0
    block_width = dx * 0.9
    block_height = dy * 0.9
    
    # 绘制幅值比方形色块
    for x, y, amp in zip(x_coords, y_coords, amp_ratios):
        rect = Rectangle(
            (x - block_width/2, y - block_height/2),
            block_width, block_height,
            facecolor=plt.cm.viridis((amp - amp_ratios.min()) / (amp_ratios.max() - amp_ratios.min())),
            edgecolor="black", linewidth=0.5
        )
        ax3.add_patch(rect)
    
    ax3.set_xlim(x_coords.min() - dx, x_coords.max() + dx)
    ax3.set_ylim(y_coords.min() - dy, y_coords.max() + dy)
    ax3.set_title("新版本：方形色块绘图", fontsize=14)
    ax3.set_xlabel("X 坐标 (mm)")
    ax3.set_ylabel("Y 坐标 (mm)")
    ax3.set_aspect("equal")
    
    # 添加colorbar
    scatter3 = ax3.scatter([], [], c=[], cmap="viridis", vmin=amp_ratios.min(), vmax=amp_ratios.max())
    fig.colorbar(scatter3, ax=ax3, label="幅值比")
    
    # 绘制相位差方形色块
    for x, y, phase in zip(x_coords, y_coords, phase_shifts):
        rect = Rectangle(
            (x - block_width/2, y - block_height/2),
            block_width, block_height,
            facecolor=plt.cm.twilight((phase - phase_shifts.min()) / (phase_shifts.max() - phase_shifts.min())),
            edgecolor="black", linewidth=0.5
        )
        ax4.add_patch(rect)
    
    ax4.set_xlim(x_coords.min() - dx, x_coords.max() + dx)
    ax4.set_ylim(y_coords.min() - dy, y_coords.max() + dy)
    ax4.set_title("新版本：方形色块绘图", fontsize=14)
    ax4.set_xlabel("X 坐标 (mm)")
    ax4.set_ylabel("Y 坐标 (mm)")
    ax4.set_aspect("equal")
    
    # 添加colorbar
    scatter4 = ax4.scatter([], [], c=[], cmap="twilight", vmin=phase_shifts.min(), vmax=phase_shifts.max())
    fig.colorbar(scatter4, ax=ax4, label="相位差 (rad)")
    
    plt.tight_layout()
    fig.savefig("demo_comparison.png", dpi=150, bbox_inches="tight")
    print("✓ 对比图已保存为: demo_comparison.png")
    
    plt.close(fig)


if __name__ == "__main__":
    print("开始演示 post_process.py 的改进功能...")
    
    demo_calculation_separation()
    demo_square_blocks()
    demo_phase_periodicity()
    demo_comparison()
    
    print("\n=== 演示完成 ===")
    print("生成的图片文件：")
    print("- demo_square_blocks.png: 方形色块绘图效果")
    print("- demo_phase_periodicity.png: 相位周期性处理效果")
    print("- demo_comparison.png: 新旧绘图方法对比")
    print("\n改进总结：")
    print("1. ✓ 传递函数计算与绘图分离，提高重用性")
    print("2. ✓ 方形色块绘图，更适合等距网格数据")
    print("3. ✓ 相位周期性处理，避免插值时的跳跃问题")
    print("4. ✓ 保留便利函数，向后兼容")
