"""
测试 post_process.py 的修改

测试新的绘图函数功能：
1. 传递函数计算与绘图函数分离
2. 方形色块绘图效果
3. 相位周期性处理
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import List
import pytest

# 导入需要测试的模块
from sweeper400.analyze.post_process import (
    calculate_transfer_function,
    plot_transfer_function_discrete_distribution,
    plot_transfer_function_interpolated_distribution,
    plot_transfer_function_discrete_distribution_from_raw,
    plot_transfer_function_interpolated_distribution_from_raw,
    TransferFunctionResult,
)


def create_mock_transfer_function_results() -> List[TransferFunctionResult]:
    """
    创建模拟的传递函数结果数据，用于测试绘图函数
    """
    # 导入Point2D类型
    from sweeper400.use.sweeper import Point2D

    # 创建一个3x3的网格
    positions = []
    amp_ratios = []
    phase_shifts = []

    for x in [0, 1, 2]:
        for y in [0, 1, 2]:
            positions.append(Point2D(x=float(x), y=float(y)))
            # 模拟一些有趣的幅值比分布
            amp_ratios.append(1.0 + 0.3 * np.sin(x) * np.cos(y))
            # 模拟相位分布，包含一些跳跃来测试周期性处理
            phase = 0.5 * x + 0.3 * y
            if x == 2 and y == 1:  # 在某个点添加相位跳跃
                phase += np.pi  # 添加π的跳跃
            phase_shifts.append(phase)

    # 将相位归一化到[-π, π]区间
    phase_shifts = [np.arctan2(np.sin(p), np.cos(p)) for p in phase_shifts]

    # 创建TransferFunctionResult列表
    results = []
    for pos, amp, phase in zip(positions, amp_ratios, phase_shifts):
        result: TransferFunctionResult = {
            "position": pos,
            "amp_ratio": amp,
            "phase_shift": phase,
        }
        results.append(result)

    return results


def test_transfer_function_calculation_separation():
    """
    测试传递函数计算与绘图的分离
    """
    # 创建模拟数据
    tf_results = create_mock_transfer_function_results()

    # 验证数据结构
    assert len(tf_results) == 9  # 3x3网格
    assert all("position" in result for result in tf_results)
    assert all("amp_ratio" in result for result in tf_results)
    assert all("phase_shift" in result for result in tf_results)

    print("✓ 传递函数结果数据结构验证通过")


def test_discrete_distribution_plot():
    """
    测试方形色块绘图功能
    """
    tf_results = create_mock_transfer_function_results()

    # 测试绘图函数
    try:
        fig, (ax1, ax2) = plot_transfer_function_discrete_distribution(tf_results)

        # 验证返回的对象类型
        assert fig is not None
        assert ax1 is not None
        assert ax2 is not None

        # 验证子图标题
        assert "幅值比" in ax1.get_title()
        assert "相位差" in ax2.get_title()

        plt.close(fig)  # 关闭图形以释放内存
        print("✓ 方形色块绘图功能测试通过")

    except Exception as e:
        pytest.fail(f"方形色块绘图测试失败: {e}")


def test_interpolated_distribution_plot():
    """
    测试插值分布绘图功能（包含相位周期性处理）
    """
    tf_results = create_mock_transfer_function_results()

    # 测试插值绘图函数
    try:
        fig, (ax1, ax2) = plot_transfer_function_interpolated_distribution(
            tf_results,
            interpolation_method="linear",  # 使用linear避免可能的cubic插值问题
            grid_resolution=20,  # 使用较小的分辨率加快测试
        )

        # 验证返回的对象类型
        assert fig is not None
        assert ax1 is not None
        assert ax2 is not None

        # 验证子图标题
        assert "幅值比" in ax1.get_title()
        assert "相位差" in ax2.get_title()

        plt.close(fig)  # 关闭图形以释放内存
        print("✓ 插值分布绘图功能（含相位周期性处理）测试通过")

    except Exception as e:
        pytest.fail(f"插值分布绘图测试失败: {e}")


def test_phase_periodicity_handling():
    """
    测试相位周期性处理
    """
    # 导入Point2D类型
    from sweeper400.use.sweeper import Point2D

    # 创建包含相位跳跃的测试数据（2x2网格）
    tf_results = []

    # 创建一个2x2网格，其中包含相位跳跃
    for i, x in enumerate([0, 1]):
        for j, y in enumerate([0, 1]):
            # 在某些点添加相位跳跃
            if i == 0:
                phase = -np.pi + 0.1  # 接近-π
            else:
                phase = np.pi - 0.1  # 接近π

            result: TransferFunctionResult = {
                "position": Point2D(x=float(x), y=float(y)),
                "amp_ratio": 1.0,
                "phase_shift": phase,
            }
            tf_results.append(result)

    # 测试插值是否能正确处理相位跳跃
    try:
        fig, (ax1, ax2) = plot_transfer_function_interpolated_distribution(
            tf_results,
            interpolation_method="linear",
            grid_resolution=10,
        )

        plt.close(fig)
        print("✓ 相位周期性处理测试通过")

    except Exception as e:
        pytest.fail(f"相位周期性处理测试失败: {e}")


def test_convenience_functions():
    """
    测试便利函数（需要模拟原始数据，这里只测试函数存在性）
    """
    # 验证便利函数存在
    assert callable(plot_transfer_function_discrete_distribution_from_raw)
    assert callable(plot_transfer_function_interpolated_distribution_from_raw)

    print("✓ 便利函数存在性验证通过")


if __name__ == "__main__":
    print("开始测试 post_process.py 的修改...")

    test_transfer_function_calculation_separation()
    test_discrete_distribution_plot()
    test_interpolated_distribution_plot()
    test_phase_periodicity_handling()
    test_convenience_functions()

    print("\n所有测试通过！✓")
    print("\n修改总结：")
    print("1. ✓ 传递函数计算与绘图函数已分离")
    print("2. ✓ 离散分布图已改为方形色块（像素画效果）")
    print("3. ✓ 插值分布图已添加相位周期性处理")
    print("4. ✓ 添加了便利函数支持直接从原始数据绘图")
