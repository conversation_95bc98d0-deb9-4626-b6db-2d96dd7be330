# pyright: basic
from sweeper400.use.sweeper import (
    load_measurement_data,
)
from sweeper400.analyze import (
    plot_transfer_function_discrete_distribution,
    plot_transfer_function_interpolated_distribution,
)

# save_path = "D:\\EveryoneDownloaded\\test_data.pkl"
save_path = "D:\\EveryoneDownloaded\\半场.pkl"
measurement_data = load_measurement_data(save_path)

fig, (ax1, ax2) = plot_transfer_function_discrete_distribution(
    measurement_data, save_path="D:\\EveryoneDownloaded\\transfer_function.png"
)
fig, (ax1, ax2) = plot_transfer_function_interpolated_distribution(
    measurement_data,
    save_path="D:\\EveryoneDownloaded\\transfer_function_interpolated.png",
)
