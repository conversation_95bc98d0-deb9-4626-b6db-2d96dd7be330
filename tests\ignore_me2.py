# pyright: basic
from sweeper400.use.sweeper import (
    load_measurement_data,
)
from sweeper400.analyze import (
    calculate_transfer_function,
    plot_transfer_function_discrete_distribution,
    plot_transfer_function_interpolated_distribution,
    plot_transfer_function_instantaneous_field,
)

# save_path = "D:\\EveryoneDownloaded\\test_data.pkl"
save_path = "D:\\EveryoneDownloaded\\半场.pkl"
measurement_data = load_measurement_data(save_path)

tf = calculate_transfer_function(measurement_data)
_ = plot_transfer_function_discrete_distribution(
    tf, save_path="D:\\EveryoneDownloaded\\transfer_function.png"
)
_ = plot_transfer_function_interpolated_distribution(
    tf,
    save_path="D:\\EveryoneDownloaded\\transfer_function_interpolated.png",
)
_ = plot_transfer_function_instantaneous_field(
    tf, save_path="D:\\EveryoneDownloaded\\instantaneous_field.png"
)
