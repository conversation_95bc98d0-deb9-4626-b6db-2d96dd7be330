"""
# analyze子包

子包路径：`sweeper400.analyze`

包含**信号和数据处理**相关的模块和类。
"""

# 将模块功能提升至包级别，可缩短外部import语句
from .my_dtypes import (
    PositiveInt,
    PositiveFloat,
    SamplingInfo,
    init_sampling_info,
    SineArgs,
    init_sine_args,
    Waveform,
)
from .basic_sine import (
    get_sine,
    get_sine_cycles,
    estimate_sine_args,
    extract_single_tone_information_vvi,
)
from .waveform_generator import WaveformGenerator, SineGenerator
from .post_process import (
    TransferFunctionResult,
    calculate_transfer_function,
    plot_transfer_function_discrete_distribution,
    plot_transfer_function_interpolated_distribution,
    plot_transfer_function_instantaneous_field,
    plot_transfer_function_discrete_distribution_from_raw,
    plot_transfer_function_interpolated_distribution_from_raw,
    plot_transfer_function_instantaneous_field_from_raw,
)

# 控制 import * 的行为
__all__ = [
    "PositiveInt",
    "PositiveFloat",
    "SamplingInfo",
    "init_sampling_info",
    "SineArgs",
    "init_sine_args",
    "Waveform",
    "get_sine",
    "get_sine_cycles",
    "estimate_sine_args",
    "extract_single_tone_information_vvi",
    "WaveformGenerator",
    "SineGenerator",
    "TransferFunctionResult",
    "calculate_transfer_function",
    "plot_transfer_function_discrete_distribution",
    "plot_transfer_function_interpolated_distribution",
    "plot_transfer_function_instantaneous_field",
    "plot_transfer_function_discrete_distribution_from_raw",
    "plot_transfer_function_interpolated_distribution_from_raw",
    "plot_transfer_function_instantaneous_field_from_raw",
]
